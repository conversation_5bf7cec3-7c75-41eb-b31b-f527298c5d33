<script setup>
import { ref } from "vue";
import TopTitle from "@/views/components/TopTitle/index.vue";
import <PERSON><PERSON><PERSON> from "./components/PieChart.vue";
import {
  Search,
  Refresh,
  Download,
  Document,
  CircleCheck,
  SuccessFilled,
  Files,
} from "@element-plus/icons-vue";

// 响应式数据
const dateRange = ref(["2025-01-15", "2025-01-21"]);
const selectedRegion = ref("");
const selectedStatus = ref("全部状态");

// 统计数据
const statsData = ref({
  totalComplaints: 1248,
  processingRate: 92.3,
  approvalRate: 87.6,
  provincialSupervision: 24,
});

// 饼图数据
const complainantData = ref([
  { value: 35, name: "学生家长" },
  { value: 25, name: "管理人员" },
  { value: 20, name: "教职工" },
  { value: 20, name: "其他人员" },
]);

const schoolTypeData = ref([
  { value: 30, name: "幼儿园" },
  { value: 25, name: "小学" },
  { value: 20, name: "普通高中" },
  { value: 15, name: "中等学校" },
  { value: 10, name: "其他" },
]);

// 饼图颜色配置
const complainantColors = ["#5470c6", "#91cc75", "#fac858", "#ee6666"];
const schoolTypeColors = [
  "#5470c6",
  "#91cc75",
  "#fac858",
  "#ee6666",
  "#73c0de",
];

// 方法
const handleRefresh = () => {
  console.log("刷新数据");
};

const handleExport = () => {
  console.log("导出数据");
};

const handleSearch = () => {
  console.log("搜索地区:", selectedRegion.value);
};
</script>

<template>
  <div class="data-board">
    <TopTitle title="数据看板"></TopTitle>

    <!-- 第一行：筛选条件和操作按钮 -->
    <div class="filter-section">
      <div class="filter-left">
        <!-- 时间选择 -->
        <div class="date-picker-wrapper">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            size="default"
          />
        </div>

        <!-- 搜索地区 -->
        <div class="search-wrapper">
          <el-input
            v-model="selectedRegion"
            placeholder="搜索地区"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
      </div>

      <div class="filter-right">
        <!-- 状态选择 -->
        <el-select
          v-model="selectedStatus"
          placeholder="选择状态"
          size="default"
        >
          <el-option label="全部状态" value="全部状态" />
          <el-option label="办理中（省厅督办件）" value="办理中" />
          <el-option label="已办结（省厅督办件）" value="已办结" />
        </el-select>

        <!-- 刷新数据按钮 -->
        <el-button @click="handleRefresh" :icon="Refresh"> 刷新数据 </el-button>

        <!-- 导出数据按钮 -->
        <el-button type="primary" @click="handleExport" :icon="Download">
          导出数据
        </el-button>
      </div>
    </div>

    <!-- 第二行：统计卡片 -->
    <div class="stats-section">
      <!-- 总投诉量 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">总投诉量</div>
          <div class="stats-value">
            {{ statsData.totalComplaints.toLocaleString() }}
          </div>
        </div>
        <div class="stats-icon complaints-icon">
          <img src="@/assets/images/data-board/1.png" alt="" />
        </div>
      </div>

      <!-- 处理率 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">处理率</div>
          <div class="stats-value">{{ statsData.processingRate }}%</div>
        </div>
        <div class="stats-icon processing-icon">
          <img src="@/assets/images/data-board/2.png" alt="" />
        </div>
      </div>

      <!-- 审核通过率 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">审核通过率</div>
          <div class="stats-value">{{ statsData.approvalRate }}%</div>
        </div>
        <div class="stats-icon approval-icon">
          <img src="@/assets/images/data-board/3.png" alt="" />
        </div>
      </div>

      <!-- 省厅督办件 -->
      <div class="stats-card">
        <div class="stats-content">
          <div class="stats-title">省厅督办件</div>
          <div class="stats-value">{{ statsData.provincialSupervision }}</div>
        </div>
        <div class="stats-icon supervision-icon">
          <img src="@/assets/images/data-board/4.png" alt="" />
        </div>
      </div>
    </div>

    <!-- 图表区域第一行 -->
    <div class="chart-section-1">
      <div class="chart-row">
        <!-- 举报人身份分布 -->
        <div class="chart-item">
          <div class="text-[18px] grey1 text-[600]">举报人身份分布</div>
          <PieChart
            :chart-data="complainantData"
            :colors="complainantColors"
            height="350px"
          />
        </div>

        <!-- 学校类型分布 -->
        <div class="chart-item">
          <div class="text-[18px] grey1 text-[600]">学校类型分布</div>
          <PieChart
            :chart-data="schoolTypeData"
            :colors="schoolTypeColors"
            height="350px"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-board {
  padding: 20px;

  .filter-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
      0px 1px 2px -1px rgba(0, 0, 0, 0.1);

    .filter-left {
      display: flex;
      align-items: center;
      gap: 16px;

      .date-picker-wrapper {
        .el-date-editor {
          width: 280px;
        }
      }

      .search-wrapper {
        .el-input {
          width: 200px;
        }
      }
    }

    .filter-right {
      display: flex;
      align-items: center;
      gap: 12px;

      .el-select {
        width: 180px;
      }
    }

    :deep(.el-input) {
      .el-input__wrapper {
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px;
      }
    }

    :deep(.el-select) {
      .el-select__wrapper {
        width: 192px !important;
        height: 40px !important;
        line-height: 40px !important;
        font-size: 16px;
      }
    }

    :deep(.el-date-editor) {
      height: 40px !important;
      line-height: 40px !important;
    }
  }

  .stats-section {
    display: flex;
    gap: 15px;
    margin: 16px 0;

    .stats-card {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 24px;
      background: #fff;
      border-radius: 8px;
      box-shadow: 0px 1px 3px 0px rgba(0, 0, 0, 0.1),
        0px 1px 2px -1px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease;

      &:hover {
        transform: translateY(-2px);
      }

      .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;

        img {
          width: 48px;
          height: 48px;
          object-fit: contain;
        }
      }

      .stats-content {
        flex: 1;

        .stats-title {
          font-size: 14px;
          color: #666;
          margin-bottom: 8px;
        }

        .stats-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
          line-height: 1;
        }
      }
    }
  }

  .chart-section-1 {
    margin: 20px 0;

    .chart-row {
      display: flex;
      gap: 20px;

      .chart-item {
        flex: 1;
        min-height: 392px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        padding: 20px;
      }
    }
  }
}
</style>
