<script setup>
import { EchartsUI, useEcharts } from "@/components/Echart"
import { ref, watch, onMounted } from "vue"

const props = defineProps({
  title: {
    type: String,
    default: "饼图"
  },
  chartData: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: "300px"
  },
  colors: {
    type: Array,
    default: () => ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4']
  }
})

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

function paintChart() {
  if (!props.chartData || props.chartData.length === 0) return

  renderEcharts({
    backgroundColor: "#fff",
    color: props.colors,
    // tooltip: {
    //   trigger: 'item',
    //   formatter: '{b}: {c} ({d}%)'
    // },
    legend: {
      orient: 'vertical',
      right: 20,
      top: 'center',
      itemWidth: 12,
      itemHeight: 12,
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      formatter: function(name) {
        return name
      }
    },
    series: [
      {
        name: props.title,
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['35%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
        },
        label: {
          show: true,
          position: 'outside',
          fontSize: 12,
          color: '#666',
          formatter: '{b}',
          distanceToLabelLine: 5
        },
        emphasis: {
          label: {
            show: true,
            position: 'outside',
            fontSize: 12,
            color: '#666',
            formatter: '{b}',
            distanceToLabelLine: 5
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 10,
            lineStyle: {
              color: '#999',
              width: 1
            }
          },
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        labelLine: {
          show: true,
          length: 15,
          length2: 10,
          lineStyle: {
            color: '#999',
            width: 1
          }
        },
        data: props.chartData
      }
    ]
  })
}
</script>

<template>
  <div class="pie-chart-container">
    <EchartsUI ref="chartRef" :height="props.height" />
  </div>
</template>

<style scoped>
.pie-chart-container {
  width: 100%;
}
</style>
